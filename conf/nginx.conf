user  nginx;
worker_processes auto;

pid        /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$http_x_forwarded_for $remote_addr $http_cip - $remote_user [$time_local] $request_time $upstream_response_time "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent"';

    client_max_body_size 1000M;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    server {
        listen 25049;

        root /usr/share/nginx/html;
        client_max_body_size 20M;
        add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        expires off;

        gzip on;
        gzip_proxied any;
        gzip_http_version 1.0;

        gzip_types
            text/css
            text/plain
            text/javascript
            application/javascript
            application/json
            application/x-javascript
            application/xml
            application/xml+rss
            application/xhtml+xml
            application/x-font-ttf
            application/x-font-opentype
            application/vnd.ms-fontobject
            image/svg+xml
            image/x-icon
            font/ttf
            font/woff
            application/rss+xml
            application/atom_xml;

        gzip_vary on;
        gzip_buffers 16 8k;
        gzip_min_length 50;

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
            set $hour $4;
            set $minutes $5;
            set $seconds $6;
        }

        access_log /logs/nginx-access.$year-$month-$day-$hour.log;
        error_log /logs/nginx-error.log;

        # 处理单页应用路由
        location / {
            index index.html;
            try_files $uri $uri/ /index.html;
        }
        
        # 禁止访问 . 开头的隐藏文件 
        location ~ /\. {
            deny all;
        }
    }

}