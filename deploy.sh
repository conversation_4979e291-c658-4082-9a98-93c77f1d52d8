#!/bin/bash

# 通过kunkka部署

set -xe

mkdir -p /data0/vad/fans-omniview-frontend && mkdir -p /data0/collect/fans-omniview-frontend

cd /data0/vad/fans-omniview-frontend

wget -q $release_url -O /tmp/fans-omniview-frontend.tgz

if ! tar xOf /tmp/fans-omniview-frontend.tgz &> /dev/null;
then
   echo "tar xOf fans-omniview-frontend.tgz fail"
   exit 1
else
    echo "tar xOf fans-omniview-frontend.tgz success"
fi

echo "Preparing to start Docker container"

rm -rf *

tar -zxf /tmp/fans-omniview-frontend.tgz -C /data0/vad/fans-omniview-frontend

# Stop previous containers if running
echo "Stopping previous containers if they exist..."
docker-compose down

# Wait to ensure ports are released
sleep 2

# Start Docker container
echo "Starting containers..."
docker-compose up -d

# Check if the container started successfully
if [ $? -eq 0 ]; then
    echo "Deployment successful!"
    echo "=========================================="
    echo "Frontend service has been deployed successfully"
    echo "=========================================="
else
    echo "Docker container failed to start. Please check the logs"
    exit 1
fi
