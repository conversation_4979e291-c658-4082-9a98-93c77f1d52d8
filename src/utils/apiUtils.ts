/**
 * API工具函数
 * 提供API相关的工具函数和常量
 */

// 成功状态码
export const API_SUCCESS_CODE = 1000;

// 常见错误码映射
export const API_ERROR_CODES = {
  SYSTEM_ERROR: 1001,
  NOT_EXISTS: 1002,
  ALREADY_EXISTS: 1003,
  INVALID_PARAM: 1004,
  OPERATION_FAILED: 1005,
  THIRD_PARTY_ERROR: 1006,
  UNAUTHORIZED: 1007,
  FORBIDDEN: 1008,
};

/**
 * 检查API响应状态码是否成功
 * @param code API响应状态码
 * @returns 是否成功
 */
export const isApiSuccess = (code: number): boolean => {
  return code === API_SUCCESS_CODE;
};

/**
 * 获取API错误信息
 * @param code API错误码
 * @returns 错误信息
 */
export const getApiErrorMessage = (code: number): string => {
  switch (code) {
    case API_ERROR_CODES.SYSTEM_ERROR:
      return '系统错误';
    case API_ERROR_CODES.NOT_EXISTS:
      return '记录不存在';
    case API_ERROR_CODES.ALREADY_EXISTS:
      return '记录已存在';
    case API_ERROR_CODES.INVALID_PARAM:
      return '参数无效';
    case API_ERROR_CODES.OPERATION_FAILED:
      return '操作失败';
    case API_ERROR_CODES.THIRD_PARTY_ERROR:
      return '第三方服务错误';
    case API_ERROR_CODES.UNAUTHORIZED:
      return '未授权访问';
    case API_ERROR_CODES.FORBIDDEN:
      return '禁止访问';
    default:
      return '未知错误';
  }
};
