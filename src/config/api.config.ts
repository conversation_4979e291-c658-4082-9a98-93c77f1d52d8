/**
 * API 配置文件
 * 集中管理所有 API 相关的配置，如基础 URL
 * 直接从环境变量读取配置，不使用默认值
 */

// 环境变量
const env = import.meta.env;

// 环境检测
const isProd = env.PROD;

// 输出当前加载的环境变量，帮助调试
if (!isProd) {
  console.log('当前环境模式:', env.MODE);
  console.log('全部环境变量:', Object.keys(env));
  console.log('VITE_APP_* 变量:', Object.keys(env).filter(key => key.startsWith('VITE_APP_')));
}

// 配置对象直接从环境变量中获取
const CONFIG = {
  BASE_URL: env.VITE_APP_BASE_URL,
  API_PATH: env.VITE_APP_API_PATH,
  TOKEN_PATH: env.VITE_APP_TOKEN_PATH,
  CAS_BASE_URL: env.VITE_APP_CAS_BASE_URL,
  CALLBACK_URL: env.VITE_APP_CALLBACK_URL,
  OPENAI_BASE_URL: env.VITE_APP_OPENAI_BASE_URL,
  OPENAI_API_KEY: env.VITE_APP_OPENAI_API_KEY
};

// 调试日志
if (!isProd) {
  console.log('API Config:', CONFIG);
  console.log('VITE_APP 变量列表:', Object.keys(env).filter(key => key.startsWith('VITE_APP_')));
}

// API基础URL
export const API_BASE_URL = CONFIG.BASE_URL;

// 编码URL参数的辅助函数
const encodeURL = (url: string) => encodeURIComponent(url);

// API 路径
export const API_PATHS = {
  // 通用API路径
  API: `${API_BASE_URL}${CONFIG.API_PATH}`,
  
  // 认证相关API
  TOKEN: `${API_BASE_URL}${CONFIG.TOKEN_PATH}`,
  
  // 回调地址 
  CALLBACK_URL: CONFIG.CALLBACK_URL,
  
  // CAS 登录URL(已包含回调地址)
  CAS_LOGIN: `${CONFIG.CAS_BASE_URL}?service=${encodeURL(CONFIG.CALLBACK_URL)}`
};

// OpenAI API 配置
export const OPENAI_CONFIG = {
  BASE_URL: CONFIG.OPENAI_BASE_URL,
  API_KEY: CONFIG.OPENAI_API_KEY,
};

// 导出当前环境信息
export const ENV_INFO = {
  isProd,
  envName: isProd ? '生产环境' : '开发环境'
};
