/**
 * markdownWorker.ts
 * 该Web Worker用于在后台线程处理Markdown渲染，避免阻塞主UI线程
 */

//导入所需的库
import { processMarkdown } from '../utils/markedConfig';

// 声明Worker上下文类型
declare const self: Worker;

// Worker消息数据接口
interface WorkerMessageData {
  markdown: string;  // 要处理的markdown内容
  id: string;        // 消息标识符
}

// Worker响应数据接口
interface WorkerResponseData {
  id: string;        // 消息标识符
  html?: string;     // 处理后的HTML
  error?: string;    // 错误信息
  success: boolean;  // 是否成功
}

// 处理来自主线程的消息
self.onmessage = function(e: MessageEvent<WorkerMessageData>): void {
  try {
    const { markdown, id } = e.data;
    
    if (!markdown) {
      self.postMessage({ 
        id, 
        error: '没有提供Markdown内容', 
        success: false 
      } as WorkerResponseData);
      return;
    }
    
    // 处理Markdown
    const htmlContent = processMarkdown(markdown);
    
    // 发送结果回主线程
    self.postMessage({
      id,
      html: htmlContent,
      success: true
    } as WorkerResponseData);
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    self.postMessage({
      id: e.data.id,
      error: errorMessage,
      success: false
    } as WorkerResponseData);
  }
};
