/* Markdown内容样式 */
.markdown-content {
  /* 基础样式 */
  font-size: 14px;
  line-height: 1.6;
}

/* 段落样式 */
.markdown-content p {
  margin: 0.5em 0;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

/* 行内代码样式 */
.markdown-content code {
  background: rgba(0, 0, 0, 0.06);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

/* 代码块样式 */
.markdown-content pre {
  /*background: rgba(0, 0, 0, 0.06);*/
  background: transparent;
  padding: 0.2em;
  border-radius: 5px;
  overflow-x: auto;
  /* margin: 0.8em 0; */
}

.markdown-content pre code {
  background: transparent;
  padding: 0;
  border-radius: 0;
  display: block;
  line-height: 1.5;
}

/* 引用样式 */
.markdown-content blockquote {
  border-left: 4px solid #ddd;
  padding: 0 1em;
  color: #666;
  margin: 0.5em 0;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 500;
}

/* 表格样式 */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.8em 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #eee;
  padding: 0.5em;
  text-align: left;
}

.markdown-content th {
  background-color: #f5f5f5;
}

/* 链接样式 */
.markdown-content a {
  color: #1890ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.8em 0;
}

/* details 和 summary 标签样式 */
.markdown-content details {
  border-radius: 6px;
  padding: 0.5em;
  margin: 0.8em 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.markdown-content summary {
  cursor: pointer;
  font-weight: 500;
  padding: 0.3em 0;
  user-select: none;
  color: rgba(0, 0, 0, 0.85);
}

.markdown-content details[open] summary {
  margin-bottom: 0.5em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.5em;
}

/* 工具调用样式 */
.markdown-content summary {
  list-style: none; /* 移除列表样式 */
}

.markdown-content summary::marker,
.markdown-content summary::-webkit-details-marker {
  display: none; /* 隐藏默认三角形 */
}

.markdown-content summary:before {
  content: '▶'; /* 使用三角形符号 */
  display: inline-block;
  margin-right: 0.5em;
  font-size: 0.8em;
  transition: transform 0.2s;
}

.markdown-content details[open] summary:before {
  transform: rotate(90deg);
}

/* 懒加载占位符样式 */
.markdown-placeholder,
.lazy-placeholder {
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f5f5;
  color: #888;
  text-align: center;
  font-size: 14px;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 0;
  animation: pulse 1.5s infinite ease-in-out;
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 0.9; }
  100% { opacity: 0.6; }
}
