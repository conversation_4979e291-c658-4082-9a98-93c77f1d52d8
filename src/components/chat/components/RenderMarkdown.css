/* Markdown 表格样式 */
.markdown-table-container {
  width: 100%;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
}

.markdown-table-head {
  background-color: #f5f5f5;
}

.markdown-table-header {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  border: 1px solid #e8e8e8;
}

.markdown-table-row:hover {
  background-color: #f9f9f9;
}

.markdown-table-cell {
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
}

/* 确保表格在移动设备上也能正常显示 */
@media (max-width: 768px) {
  .markdown-table-container {
    width: 100%;
    overflow-x: auto;
  }
  
  .markdown-table {
    min-width: 600px;
  }
}


/* 代码块滑动条样式 - 高对比度灰色设计 */
.hljs-enabled-content code.hljs {
  /* Firefox兼容性 */
  scrollbar-width: thin;
  scrollbar-color: #888888 #1a1b26;
}

/* 滑动条宽度 - 使用更宽的滑动条 */
.hljs-enabled-content code.hljs::-webkit-scrollbar {
  width: 10px; 
  height: 10px;
  position: absolute;
  right: 0;
}

/* 设置滑块外观 - 使用高对比度的灰色，在深色背景上清晰可见 */
.hljs-enabled-content code.hljs::-webkit-scrollbar-thumb {
  background-color: #888888;
  border-radius: 4px;
  border: 2px solid #1a1b26;
  box-shadow: 0 0 5px rgba(150, 150, 150, 0.5);
}

/* 滑道使用深色背景，增强滑块的可见度 */
.hljs-enabled-content code.hljs::-webkit-scrollbar-track {
  background-color: #1a1b26;
  border-radius: 5px;
}

/* 悬停状态 - 增加亮度和发光效果 */
.hljs-enabled-content code.hljs::-webkit-scrollbar-thumb:hover {
  background-color: #aaaaaa;
  box-shadow: 0 0 8px rgba(170, 170, 170, 0.8);
}

/* 滑动状态 - 最大亮度 */
.hljs-enabled-content code.hljs::-webkit-scrollbar-thumb:active {
  background-color: #cccccc;
  box-shadow: 0 0 12px rgba(200, 200, 200, 0.9);
}

/* 增强代码块显示效果，添加最大高度和滑动条 */
.hljs-enabled-content code.hljs {
  padding: 16px;
  border-radius: 8px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Monaco, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  /* Tokyo Night Dark 主题颜色 */
  background-color: #1a1b26 !important;
  color: #9aa5ce !important;
}

/* 确保代码块底部有足够的间距 */
.hljs-enabled-content pre {
  margin-bottom: 16px;
}
