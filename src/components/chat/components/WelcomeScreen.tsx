import {
  EllipsisOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { Welcome } from '@ant-design/x';
import { Button, Space } from 'antd';
import React from 'react';

// 如果将来需要添加属性，可以在这里定义接口

const WelcomeScreen: React.FC = () => {
  return (
    <Space direction="vertical" size={16} className="placeholder-container">
      <Welcome
        variant="borderless"
        icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
        title="您好，我是粉条业务智能助手"
        description="为您提供订单查询、用户分析、故障排查及数据洞察，助力业务决策更高效"
        extra={
          <Space>
            <Button icon={<ShareAltOutlined />} />
            <Button icon={<EllipsisOutlined />} />
          </Space>
        }
      />
    </Space>
  );
};

export default WelcomeScreen;
