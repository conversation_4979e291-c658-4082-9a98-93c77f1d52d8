import { Bubble } from '@ant-design/x';
import DOMPurify from 'dompurify';
import React, { useEffect, useState } from 'react';
import { useMarkdownWorker } from '../hooks/useMarkdownWorker';
import { processMarkdown } from '../utils/markedConfig';
import { isWorkerSupported } from '../utils/workerHelper';

// 导入自定义样式
import 'highlight.js/styles/tokyo-night-dark.css';
import './RenderMarkdown.css';

// 条件判断函数：决定是否使用 Web Worker 渲染 Markdown
export type ShouldUseWorkerFn = (content: string) => boolean;

export const CONTENT_THRESHOLD = 1000;

// 默认的判断函数实现
const defaultShouldUseWorker: ShouldUseWorkerFn = (content: string) => {
  // 默认情况下，满足以下条件时使用 Worker：
  // 1. 浏览器支持 Web Worker
  // 2. 内容长度超过一定阈值（这里设为 1000 字符）
  return isWorkerSupported() && content.length > CONTENT_THRESHOLD; 
};

/**
 * 优化的Markdown渲染组件
 * 
 * 性能优化：
 * 1. 使用React.memo防止不必要的重渲染
 * 2. 使用useMemo缓存组件配置
 */
export interface RenderMarkdownProps {
  content: string;
  className?: string;
  onProcessed?: (content: string, success: boolean) => void; // 添加处理完成回调
  shouldUseWorker?: ShouldUseWorkerFn; // 添加条件开关函数，决定是否使用 Web Worker
}

/**
 * Markdown渲染组件
 */
export const RenderMarkdown: React.FC<RenderMarkdownProps> = React.memo(function RenderMarkdown({
  content,
  className = '',
  onProcessed,
  shouldUseWorker = defaultShouldUseWorker,
}) {
  // 判断是否使用 Worker
  const useWorker = shouldUseWorker(content);
  
  // 调用Worker Hook - 始终调用，但在不需要时将使用空字符串做为内容
  const { isLoading, processedHTML, error } = useMarkdownWorker({
    // 只在需要使用 Worker 时传入实际内容
    content: useWorker ? content : '',
    onProcessed: useWorker ? onProcessed : undefined
  });
  
  // 处理同步渲染的逻辑
  const [syncProcessedHTML, setSyncProcessedHTML] = useState<string>('');
  
  // 如果不使用 Worker，则直接同步渲染
  useEffect(() => {
    if (!useWorker && content) {
      const html = DOMPurify.sanitize(processMarkdown(content));
      setSyncProcessedHTML(html);
      
      // 同步渲染完成回调
      if (onProcessed) {
        onProcessed(content, true);
      }
    }
  }, [content, useWorker, onProcessed]);

  return (
    <div className={`markdown-output-wrapper ${className || ''}`}>
      {/* 在使用 Worker 且内容较多时才显示加载状态 */}
      {isLoading && useWorker && (
        <div className="markdown-loading">
          <Bubble 
            loading={true}
            content=""
            style={{ paddingLeft: 0 }}
          />
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="markdown-error">{error}</div>
      )}

      {/* 渲染处理过的内容 */}
      <div 
        className="hljs-enabled-content markdown-body" 
        dangerouslySetInnerHTML={{
          __html: useWorker ? processedHTML : syncProcessedHTML
        }} 
      />
    </div>
  );
});

export default RenderMarkdown;

