import { Sender } from '@ant-design/x';
import React from 'react';

/**
 * 聊天输入组件
 * 负责处理用户输入和发送消息
 */
const ChatInput: React.FC<{
  value: string;
  onChange: (value: string) => void;
  onSend: (content: string) => void;
  onCancel: () => void;
  loading?: boolean; // 运行中状态
}> = React.memo(({ value, onChange, onSend, onCancel, loading = false }) => {
  return (
    <Sender
      value={value}
      onChange={onChange}
      onSubmit={() => {
        if (value.trim()) {
          onSend(value);
          onChange('');
        }
      }}
      placeholder="输入您的问题..."
      loading={loading}
      onCancel={onCancel}
    />
  );
});

export default ChatInput;
