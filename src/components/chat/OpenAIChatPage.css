/* 根节点高度链路，确保主内容撑满全屏 */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

.chat-page-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    background: #fff;
    font-family: AlibabaPuHuiTi, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}
  
  .chat-menu {
    background: #ffffff;
    width: 280px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
  }
  
  /* 主内容滚动容器，唯一滚动条出现位置 */
.chat-content-wrapper {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #ffffff;
    overflow: auto; /* 滚动条只在这里 */
    min-width: 0;
    min-height: 0;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    transition: margin 0.3s, width 0.3s;
}
  
  .chat-logo {
    display: flex;
    height: 72px;
    align-items: center;
    justify-content: start;
    padding: 0 24px;
    box-sizing: border-box;
  }
  
  .logo-img {
    width: 24px;
    height: 24px;
    display: inline-block;
  }
  
  .logo-text {
    display: inline-block;
    margin: 0 8px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.88);
    font-size: 16px;
  }
  
  .add-conversation-btn {
    background: rgba(22, 119, 255, 0.06);
    border: 1px solid rgba(22, 119, 255, 0.2);
    width: calc(100% - 24px);
    margin: 0 12px 24px 12px;
  }
  
  .conversations-container {
    overflow-y: auto;
    flex: 1;
    height: calc(100% - 120px);
    padding-bottom: 70px; /* 为底部用户信息区域留出空间 */
  }
  
  .conversation-group {
    margin-bottom: 16px;
  }
  
  .conversation-date-label {
    padding: 8px 0 4px 12px;
    font-weight: bold;
    color: #888;
    font-size: 15px;
  }
  
  /* 主内容区，宽度居中，自动撑满剩余空间 */
.chat-content {
    width: 100%;
    max-width: 800px;
    flex: 1 1 auto;
    min-height: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 24px;
    gap: 16px;
    /* 移除height: 100%和overflow: hidden，避免影响滚动 */
    position: relative;
    background-color: #fff; /* 添加背景色，确保不透明 */
}
  
  /* 确保输入框固定在底部，与.chat-content对齐 */
  .chat-content .ant-sender {
    position: fixed;
    bottom: 24px;
    background-color: #fff;
    z-index: 20; /* 增加z-index值，确保输入框始终在消息容器之上 */
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    max-width: 752px; /* 限制最大宽度，与内容区域保持一致（chat-content的max-width - 两侧内边距） */
    /* 基础样式，会被下面的规则覆盖 */
    left: 0;
    right: 0;
    margin: 0 auto;
    width: calc(100% - 48px);
    /* 添加过渡效果，与聊天区域保持一致 */
    transition: left 0.3s ease, width 0.3s ease;
    /* 确保输入框完全不透明 */
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    padding: 6px 10px; /* 减小内边距，使输入框高度变小 */
  }
  
  /* 添加底部遮罩元素，防止文本露出 - 侧边栏展开状态 */
  .chat-page-container:not(.chat-sidebar--collapsed) .chat-content::after {
    content: '';
    position: fixed;
    left: 280px; /* 与侧边栏展开时的输入框左侧对齐 */
    right: 0;
    bottom: 0;
    height: 50px; /* 进一步减小遮罩高度 */
    background: #fff; /* 完全不透明的背景，防止下方内容透出 */
    z-index: 15; /* 低于输入框的z-index，但高于消息容器 */
    pointer-events: none; /* 允许点击穿透 */
    transition: left 0.3s ease; /* 与输入框保持相同的过渡效果 */
  }
  
  /* 添加底部遮罩元素，防止文本露出 - 侧边栏折叠状态 */
  .chat-page-container.chat-sidebar--collapsed .chat-content::after {
    content: '';
    position: fixed;
    left: 40px; /* 与侧边栏折叠时的输入框左侧对齐 */
    right: 0;
    bottom: 0;
    height: 50px; /* 进一步减小遮罩高度 */
    background: #fff; /* 完全不透明的背景，防止下方内容透出 */
    z-index: 15; /* 低于输入框的z-index，但高于消息容器 */
    pointer-events: none; /* 允许点击穿透 */
    transition: left 0.3s ease; /* 与输入框保持相同的过渡效果 */
  }
  
  /* 确保输入框外部容器也不透明 */
  .chat-content .ant-sender {
    padding: 0; /* 移除外部内边距 */
    border: none; /* 移除原有边框 */
    box-shadow: none; /* 移除原有阴影 */
  }
  
  .chat-content .ant-sender .ant-sender-content {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    padding: 0; /* 移除内边距，使输入框占据全部空间 */
    border: 1px solid #d9d9d9; /* 默认边框颜色 */
    transition: all 0.3s ease; /* 添加过渡效果 */
    position: relative; /* 确保定位正确 */
    height: 40px; /* 固定高度 */
    display: flex;
    align-items: center;
  }
  
  /* 当输入框获得焦点时，外部容器显示蓝色边框 */
  .chat-content .ant-sender .ant-sender-content:focus-within {
    border-color: #1890ff; /* 蓝色边框 */
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); /* 添加轻微的蓝色阴影效果 */
  }
  
  /* 侧边栏展开时的输入框定位 - 使用正确的父元素选择器 */
  .chat-page-container:not(.chat-sidebar--collapsed) .chat-content-wrapper .chat-content .ant-sender {
    /* 考虑侧边栏宽度，让输入框与.chat-content对齐 */
    left: 280px; /* 侧边栏宽度 */
    right: 0;
    width: calc(100% - 328px); /* 100% - 侧边栏宽度(280px) - 左右边距(48px) */
  }
  
  /* 侧边栏折叠时的输入框定位 - 使用正确的父元素选择器 */
  .chat-page-container.chat-sidebar--collapsed .chat-content-wrapper .chat-content .ant-sender {
    left: 40px; /* 折叠后的侧边栏宽度 */
    width: calc(100% - 88px); /* 100% - 折叠侧边栏宽度(40px) - 左右边距(48px) */
  }
  
  /* 修复文本输入框的样式 */
  .chat-content .ant-sender .ant-sender-input {
    width: 100%;
    padding: 8px 40px 8px 12px; /* 左右内边距，右侧为按钮留出空间 */
    box-sizing: border-box;
    height: 40px; /* 固定高度 */
    line-height: 24px; /* 行高 */
  }
  
  /* 直接针对textarea元素添加样式 - 根据截图中的类名精确定位 */
  .chat-content .ant-sender textarea,
  .chat-content .ant-sender textarea.ant-input,
  .chat-content .ant-sender textarea.ant-sender-input,
  .chat-content .ant-sender textarea.css-dev-only-do-not-override-1v5z421,
  .chat-content .ant-sender textarea.ant-input-borderless {
    border: none; /* 移除输入框自身的边框 */
    resize: none; /* 禁止调整大小 */
    outline: none; /* 移除默认的轮廓效果 */
  }
  
  /* 修正输入框内部组件布局 */
  .chat-content .ant-sender .ant-sender-content {
    width: 100%;
    position: relative;
  }

  /* 确保发送按钮正确定位 */
  .chat-content .ant-sender .ant-sender-actions-list {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%); /* 垂直居中 */
    z-index: 2; /* 确保按钮始终在最上层 */
    line-height: 1; /* 重置行高 */
  }
  
  /* 调整发送按钮大小 */
  .chat-content .ant-sender .ant-sender-actions-list .ant-btn {
    width: 24px;
    height: 24px;
    min-width: 24px; /* 防止按钮被最小宽度覆盖 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 50%; /* 圆形按钮 */
    overflow: hidden; /* 确保内容不超出边界 */
    font-size: 12px; /* 减小图标大小 */
  }
  
  /* 确保按钮内的图标不超出边界 */
  .chat-content .ant-sender .ant-sender-actions-list .ant-btn .anticon {
    font-size: 14px;
  }
  
  /* 添加确保输入框在宽度较小的情况下也能正常显示 */
  @media screen and (max-width: 600px) {
    .chat-content .ant-sender {
      width: calc(100% - 32px);
      left: 16px;
      right: 16px;
      bottom: 16px;
    }
    
    /* 小屏幕下消息容器留出更多空间 */
    .messages-container {
      padding-bottom: 120px;
    }
  }
  
  /* 消息列表区，自动填满中间空间，不负责滚动 */
.messages-container {
    flex: 1 1 auto;
    overflow: visible;
    width: 100%;
    padding: 10px;
    /* 为固定在底部的输入框留出足够的空间 */
    padding-bottom: 100px; /* 增加底部留白，确保输入框下方没有文本露出 */
    margin-bottom: 24px;
    /* 确保消息容器在输入框上方 */
    position: relative;
    z-index: 1;
}
  
  .placeholder-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 120px; /* 为底部输入框留出空间 */
    padding-top: 32px;
  }
  
  .sender-container {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  

/* 消息列表滚动条样式 - 与侧边栏保持一致 */
/* 为消息列表容器添加滚动条样式 */
.chat-content-wrapper {
  /* 将右侧内边距减小，使滑动条更紧凑 */
  padding-right: 2px;
  /* Firefox兼容性 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.18) transparent;
  /* 滑动时滑动条需要更清晰一些 */
}

/* 滑动条宽度 - 减小宽度使其更紧凑 */
.chat-content-wrapper::-webkit-scrollbar {
  width: 4px; /* 从6px减少到4px */
  /* 设置滑动条悬浮在内容上，不占用内容空间 */
  position: absolute;
  right: 0;
}

/* 设置滑块外观 */
.chat-content-wrapper::-webkit-scrollbar-thumb {
  /* 提高透明度使滑动时更易看见 */
  background-color: rgba(0, 0, 0, 0.22);
  border-radius: 4px;
  /* 使用弱边框样式增强可见度 */
  border: 1px solid rgba(0, 0, 0, 0.03);
}

/* 设置滑道为透明 */
.chat-content-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
  /* 确保滑道不占位置 */
  margin: 2px;
}

/* 默认情况下使滑块半透明 */
.chat-content-wrapper::-webkit-scrollbar-thumb {
  opacity: 0.3;
}

/* 滑动区域悬停时增强可见度 */
.chat-content-wrapper:hover::-webkit-scrollbar-thumb {
  opacity: 0.6;
  background-color: rgba(0, 0, 0, 0.25);
}

/* 当正在滑动时显示更明显的滑块 */
.chat-content-wrapper::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 1;
}