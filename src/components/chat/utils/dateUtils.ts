/**
 * 获取今天和昨天的日期字符串
 * @returns 包含今天和昨天日期字符串的对象
 */
export const getDateStrings = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  return {
    today: today.toLocaleDateString('en-CA'),
    yesterday: yesterday.toLocaleDateString('en-CA'),
  };
};

/**
 * 根据日期字符串对会话进行分组
 * @param conversations 会话列表
 * @param todayStr 今天的日期字符串
 * @param yesterdayStr 昨天的日期字符串
 * @returns 分组后的会话列表
 */
export const groupConversationsByDate = <T extends { key: string; date: string }>(
  conversations: T[],
  todayStr: string,
  yesterdayStr: string
): [string, T[]][] => {
  // 创建三个固定分组：今天、昨天、更早之前
  const groups = {
    'today': [] as T[],
    'yesterday': [] as T[],
    'earlier': [] as T[]
  };

  // 对会话进行分组
  conversations.forEach(item => {
    // 比较日期字符串，这样更可靠
    if (item.date === todayStr) {
      // 今天的会话
      groups['today'].push(item);
    } else if (item.date === yesterdayStr) {
      // 昨天的会话
      groups['yesterday'].push(item);
    } else {
      // 更早之前的会话
      groups['earlier'].push(item);
    }
  });

  // 对每个分组内的会话进行排序，确保最新的在最上面
  Object.keys(groups).forEach(day => {
    groups[day as keyof typeof groups].sort((a, b) => {
      // 尝试将键转换为数字进行比较（如果键是时间戳）
      const keyA = Number(a.key);
      const keyB = Number(b.key);

      // 如果转换成功，则按数字大小降序排列（新的在前）
      if (!isNaN(keyA) && !isNaN(keyB)) {
        return keyB - keyA;
      }

      // 如果不是数字，则按字符串比较
      return b.key.localeCompare(a.key);
    });
  });

  // 按照今天、昨天、更早之前的顺序排列
  const result: [string, T[]][] = [];

  // 只添加非空的分组
  if (groups['today'].length > 0) {
    result.push(['today', groups['today']]);
  }

  if (groups['yesterday'].length > 0) {
    result.push(['yesterday', groups['yesterday']]);
  }

  if (groups['earlier'].length > 0) {
    result.push(['earlier', groups['earlier']]);
  }

  return result;
};
