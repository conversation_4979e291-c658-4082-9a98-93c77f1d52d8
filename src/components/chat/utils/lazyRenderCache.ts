/**
 * 懒加载渲染缓存工具
 * 用于管理LazyRender组件的缓存状态
 */

// 创建一个全局渲染缓存
const renderedCache = new Set<string>();

/**
 * 清除所有已缓存的渲染内容
 * 用于重置LazyRender组件的缓存状态
 */
export const clearLazyRenderCache = (): void => {
  renderedCache.clear();
};

/**
 * 检查内容是否已被缓存
 * @param key 缓存键
 * @returns 是否已缓存
 */
export const hasRendered = (key: string): boolean => {
  return renderedCache.has(key);
};

/**
 * 添加内容到缓存
 * @param key 缓存键
 */
export const addToCache = (key: string): void => {
  renderedCache.add(key);
};

export default {
  clearLazyRenderCache,
  hasRendered,
  addToCache
};
