/**
 * markedConfig.ts
 * marked 配置的公共模块，确保主线程和 Worker 线程使用一致的配置
 */
import hljs from 'highlight.js';
import { marked } from 'marked';

/**
 * 配置 marked 选项
 */
export const configureMarked = (): void => {
  // 基本配置
  marked.setOptions({
    gfm: true,          // GitHub风格Markdown
    breaks: true,       // 允许换行符转换为<br>
    pedantic: false,
    // 注意: 新版 marked 不支持部分旧配置项
  });

  // 使用 marked.use() 添加代码高亮扩展
  marked.use({
    extensions: [{
      name: 'code',
      renderer(token) {
        const { lang, text } = token;
        let highlightedCode = text;

        try {
          highlightedCode = hljs.highlightAuto(text).value;
        } catch (err) {
          console.error('自动代码高亮出错:', err);
          // 出错时保留原始代码
        }

        // 返回高亮后的 HTML
        return `<pre><code class="hljs ${lang ? `language-${lang}` : ''}">${highlightedCode}</code></pre>`;
      }
    }]
  });
};

/**
 * 处理 Markdown 内容并转换为 HTML
 * @param markdown 原始 markdown 文本
 * @returns 处理后的 HTML（主线程应进行安全处理）
 */
export const processMarkdown = (markdown: string): string => {
  try {
    // 使用同步模式处理
    return marked.parse(markdown, { async: false }) as string;
  } catch (error) {
    console.error('解析Markdown时出错:', error);
    return `解析错误: ${error instanceof Error ? error.message : String(error)}`;
  }
};

// 确保配置初始化
configureMarked();
