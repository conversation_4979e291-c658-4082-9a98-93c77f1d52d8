/**
 * Web Worker 辅助工具函数
 * 用于创建和管理 Web Worker 实例
 */

/**
 * 检查浏览器是否支持 Web Workers
 * 返回值表示是否支持 Worker API
 */
export function isWorkerSupported(): boolean {
  return typeof Worker !== 'undefined';
}

/**
 * 创建 Worker 实例
 * @param workerURL Worker 文件的 URL 或路径
 * @param options 可选的 Worker 创建选项
 */
export function createWorkerInstance(workerURL: string, options?: WorkerOptions): Worker {
  if (!isWorkerSupported()) {
    throw new Error('您的浏览器不支持 Web Workers');
  }

  try {
    // 创建 Worker 实例，支持模块类型
    return new Worker(workerURL, options);
  } catch (error) {
    console.error(`创建 Worker 失败: ${workerURL}`, error);
    throw new Error(`无法创建 Worker: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 创建模块类型的 Worker
 * @param workerURL Worker 模块文件的 URL 或路径
 */
export function createModuleWorker(workerURL: string): Worker {
  return createWorkerInstance(workerURL, { type: 'module' });
}

/**
 * 安全地终止 Worker 实例
 * @param worker 要终止的 Worker 实例
 */
export function terminateWorker(worker: Worker | null): void {
  if (worker) {
    try {
      worker.terminate();
    } catch (error) {
      console.error('终止 Worker 时出错:', error);
    }
  }
}
