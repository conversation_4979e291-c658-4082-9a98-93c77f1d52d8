import { useEffect, useMemo, useRef, useState } from 'react';
import { Message } from '../types';
// 导入一个工具函数，用于在滚动到底部后添加延迟
import { clearLazyRenderCache } from '../utils/lazyRenderCache';

/**
 * 自定义Hook: 管理聊天消息和滚动逻辑
 * 
 * @param xMessages XChat消息
 * @returns { formattedMessages, messagesContainerRef } 格式化的消息和容器引用
 */
export const useMessages = (xMessages: any[]) => {
  // 创建对消息容器的引用，用于滚动操作
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  // 新增对滚动容器的引用
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  
  // 利用 useMemo 高效转换消息格式
  const formattedMessages = useMemo<Message[]>(() => {
    // 过滤有效消息并转换为 MessageList 所需格式
    return xMessages
      .filter(xMsg => xMsg && xMsg.message) // 过滤无效消息
      .map(xMsg => ({
        id: xMsg.id.toString(),
        message: xMsg.message,
        type: xMsg.status === 'local' ? 'user' : 'assistant'
      }));
  }, [xMessages]);
  
  // 跟踪用户是否手动滚动
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  // 存储上一次消息数量，用于检测新消息
  const prevMessagesCountRef = useRef<number>(0);
  
  // 检测用户滚动
  useEffect(() => {
    // 获取实际的滚动容器（.chat-content-wrapper）
    const scrollContainer = document.querySelector('.chat-content-wrapper');
    // 保存到ref中供其他地方使用
    scrollContainerRef.current = scrollContainer as HTMLDivElement;
    
    if (!scrollContainer) return;
    
    const handleScroll = () => {
      // 检测用户是否向上滚动
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 10; // 允许小误差
      
      // 只有用户不在底部时才标记为已滚动
      if (!isAtBottom) {
        setUserHasScrolled(true);
      } else {
        // 当用户回到底部时，重置标记
        setUserHasScrolled(false);
      }
    };
    
    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 跟踪上一次消息的ID，用于检测新消息
  const prevLastMessageIdRef = useRef<string>('');

  
  // 用于区分初始加载和新消息
  const isInitialLoadRef = useRef<boolean>(true);

  // 处理消息变化与自动滚动
  useEffect(() => {
    if (formattedMessages.length === 0) return;
    
    // 检测是否有新消息 (通过消息数量和最后一条消息ID双重检查)
    const lastMessageId = formattedMessages[formattedMessages.length - 1]?.id || '';
    const hasNewMessages = formattedMessages.length > prevMessagesCountRef.current || 
                          (lastMessageId && lastMessageId !== prevLastMessageIdRef.current);
    
    // 更新引用值
    prevMessagesCountRef.current = formattedMessages.length;
    prevLastMessageIdRef.current = lastMessageId;
    
    // 判断是初始加载还是新消息
    if (isInitialLoadRef.current && formattedMessages.length > 0) {
      // 初始加载历史消息时，直接滚动到最后一条消息，不使用平滑滚动
      isInitialLoadRef.current = false;
      
      // 清除缓存，确保只有可见区域的内容被渲染
      clearLazyRenderCache();
      
      // 使用requestAnimationFrame确保DOM已经更新
      requestAnimationFrame(() => {
        // 再添加一个小延迟，确保消息容器已经渲染
        setTimeout(() => {
          const scrollContainer = scrollContainerRef.current;
          if (scrollContainer) {
            // 直接设置滚动位置到底部，无动画
            scrollContainer.scrollTop = scrollContainer.scrollHeight;
          }
        }, 50);
      });
    } 
    // 如果是新消息或用户未手动滚动，则平滑滚动到底部
    else if (!userHasScrolled || hasNewMessages) {
      // 使用延时定时器确保滚动发生在DOM更新之后
      const scrollTimeout = setTimeout(() => {
        // 使用正确的滚动容器
        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
          // 平滑滚动到底部
          scrollContainer.scrollTo({
            top: scrollContainer.scrollHeight,
            behavior: 'smooth'
          });
          
          // 如果是新消息触发的滚动，依然保持userHasScrolled状态
          // 这样用户可以继续查看上方消息，直到他们自己再次滚动到底部
        }
      }, 100); // 延时确保消息已渲染
      
      return () => clearTimeout(scrollTimeout);
    }
  }, [formattedMessages, userHasScrolled]);
  
  // 重置初始加载状态的方法
  const resetInitialLoadState = () => {
    isInitialLoadRef.current = true;
  };

  return {
    formattedMessages,
    messagesContainerRef,
    scrollContainerRef,
    resetInitialLoadState // 导出重置方法，便于外部在切换会话时调用
  };
};

export default useMessages;
