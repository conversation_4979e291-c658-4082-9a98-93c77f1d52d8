import { useCallback } from 'react';
import { useTitlePolling } from './useTitlePolling';

/**
 * 自定义Hook: 处理消息发送逻辑
 * 
 * @param dispatch 状态分发函数
 * @param onRequest 发送请求函数
 * @param messageApiInstance 消息提示API实例
 * @param createConversationIfNeeded 创建会话函数
 * @returns 发送消息处理函数
 */
export const useSendMessage = (
  dispatch: React.Dispatch<any>,
  onRequest: (content: string) => void,
  _messageApiInstance: any,
  createConversationIfNeeded: (content: string, activeChatId: string) => Promise<{
    success: boolean;
    chatId?: string;
    newConversation?: any;
  }>
) => {
  // 使用标题轮询Hook
  const pollConversationTitle = useTitlePolling(dispatch);
  
  /**
   * 处理消息发送
   */
  const handleSendMessage = useCallback(async (content: string, activeChatId: string) => {
    if (!content.trim()) return;
    
    // 设置发送消息状态为 true，显示加载中
    dispatch({ type: 'SET_SENDING_MESSAGE', payload: true });

    // 创建会话(如果需要)
    const result = await createConversationIfNeeded(content, activeChatId);
    
    if (!result.success) {
      // 创建失败，重置发送按钮状态
      dispatch({ type: 'SET_SENDING_MESSAGE', payload: false });
      return;
    }
    
    // 创建会话成功，且是新创建的会话
    if (result.newConversation && result.chatId) {
      // 启动轮询获取更新后的会话标题
      pollConversationTitle(result.chatId, result.newConversation);
    }

    // 发送消息
    onRequest(content);

    // 清空输入框
    dispatch({ type: 'SET_INPUT', payload: '' });
  }, [dispatch, createConversationIfNeeded, onRequest, pollConversationTitle]);

  return handleSendMessage;
};

export default useSendMessage;
