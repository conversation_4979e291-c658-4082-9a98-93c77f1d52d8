import { useRef, useCallback } from 'react';
import { getConversationDetail } from '../services/api';
import { ConversationItem } from '../types';

/**
 * 自定义Hook: 轮询获取会话最新标题
 * 由于会话创建时标题可能是临时的，需要轮询获取后端生成的最终标题
 * 
 * @param dispatch 状态分发函数
 * @param maxAttempts 最大轮询次数，默认为4
 * @param interval 轮询间隔(毫秒)，默认为3000ms
 * @returns 轮询函数
 */
export const useTitlePolling = (
  dispatch: React.Dispatch<any>, 
  maxAttempts = 4, 
  interval = 3000
) => {
  // 用于跟踪标题轮询次数
  const pollingCountRef = useRef<number>(0);
  
  /**
   * 开始轮询获取会话最新标题
   * @param chatId 会话ID
   * @param initialConversation 初始会话对象
   */
  const startPolling = useCallback((chatId: string, initialConversation: ConversationItem) => {
    // 重置轮询计数器
    pollingCountRef.current = 0;
    
    // 轮询函数
    const pollTitle = async () => {
      // 达到最大轮询次数则停止
      if (pollingCountRef.current >= maxAttempts) {
        return;
      }
      
      pollingCountRef.current += 1;
      
      try {
        // 获取会话详情
        const result = await getConversationDetail(chatId);
        
        if (result.success && result.conversation) {
          const newTitle = result.conversation.title;
          
          // 如果标题发生变化，则更新
          if (newTitle && newTitle !== initialConversation.label) {
            // 更新会话标题
            const updatedConversation = {
              ...initialConversation,
              label: newTitle
            };
            
            // 更新状态
            dispatch({ type: 'UPDATE_CONVERSATION', payload: updatedConversation });
            
            // 成功获取到新标题，停止轮询
            return;
          }
        }
        
        // 如果没有获取到新标题或标题未变，且未达到最大轮询次数，继续轮询
        if (pollingCountRef.current < maxAttempts) {
          setTimeout(pollTitle, interval);
        }
      } catch (error) {
        console.error('轮询会话标题失败:', error);
        
        // 发生错误时，如果未达到最大轮询次数，仍继续尝试
        if (pollingCountRef.current < maxAttempts) {
          setTimeout(pollTitle, interval);
        }
      }
    };
    
    // 启动首次轮询，延迟执行，给后端一些处理时间
    setTimeout(pollTitle, interval);
  }, [dispatch, maxAttempts, interval]);
  
  return startPolling;
};

export default useTitlePolling;
