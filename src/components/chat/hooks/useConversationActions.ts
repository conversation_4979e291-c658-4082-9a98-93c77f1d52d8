import { useCallback } from 'react';
import { deleteConversation, getChatMessages, createNewConversation } from '../services/api';

/**
 * 自定义Hook: 管理会话操作（点击、添加、删除等）
 *
 * @param dispatch 状态分发函数
 * @param messageApi 消息提示API实例
 * @param setMessages 设置消息函数
 * @param todayStr 今天的日期字符串
 * @param chatIdRef 外部提供的chatId引用
 * @returns 会话操作函数集合
 */
export const useConversationActions = (
  dispatch: React.Dispatch<any>,
  messageApi: any,
  setMessages: (messages: any[]) => void,
  todayStr: string,
  chatIdRef: React.RefObject<string>
) => {

  /**
   * 处理会话点击
   */
  const handleConversationClick = useCallback(async (key: string) => {
    // 设置当前活动会话
    dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: key });
    // 更新 chatIdRef，解决闭包问题
    chatIdRef.current = key;

    try {
      // 从服务器获取该会话的历史消息
      const messages = await getChatMessages(key);

      // 将服务器消息转换为 XChat 格式并设置
      if (messages && messages.length > 0) {
        const xchatMessages = messages.map(msg => ({
          id: msg.id,
          message: msg.message,
          status: msg.type === 'user' ? 'local' as const : 'success' as const
        }));

        setMessages(xchatMessages);
      } else {
        // 如果没有消息，设置空数组
        setMessages([]);
      }
    } catch (error) {
      console.error('获取历史消息失败:', error);
      messageApi.error('获取历史消息失败');

      // 出错时清空消息列表
      setMessages([]);
    }
  }, [dispatch, messageApi, setMessages]);

  /**
   * 添加新对话
   */
  const handleAddConversation = useCallback(() => {
    // 切换到空白页状态，不立即创建会话
    dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: '' });
    // 清空 XChat 消息
    setMessages([]);
  }, [dispatch, setMessages]);

  /**
   * 处理删除对话
   */
  const handleDeleteConversation = useCallback(async (key: string, activeChatId: string) => {
    try {
      const success = await deleteConversation(key);

      if (success) {
        const isActiveConversation = key === activeChatId;
        dispatch({ type: 'DELETE_CONVERSATION', payload: key });

        if (isActiveConversation) {
          dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: '' });
          // 清空 XChat 消息
          setMessages([]);
        }

        messageApi.success('删除对话成功');
      } else {
        messageApi.error('删除对话失败');
      }
    } catch (error) {
      console.error('删除对话出错:', error);
      messageApi.error('删除对话失败');
    }
  }, [dispatch, messageApi, setMessages]);

  /**
   * 处理发送消息时的会话创建
   */
  const createConversationIfNeeded = useCallback(async (content: string, activeChatId: string) => {
    // 如果已有活动会话，直接返回该 ID
    if (activeChatId) {
      chatIdRef.current = activeChatId;
      return { success: true, chatId: activeChatId };
    }

    try {
      // 调用创建会话API，传递用户消息内容，便于后端生成更有意义的会话标题
      const result = await createNewConversation(content);

      if (result.success && result.conversation) {
        // 创建成功，获取会话信息
        const { chatId, title, createTime } = result.conversation;
        const timestamp = new Date(createTime).getTime();

        // 构建新会话对象
        const newConversation = {
          key: chatId,
          label: title,
          date: todayStr,
          group: 'today',
          timestamp,
        };

        // 更新状态
        dispatch({ type: 'ADD_CONVERSATION', payload: newConversation });
        dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: chatId });

        // 更新 chatIdRef
        chatIdRef.current = chatId;

        return {
          success: true,
          chatId,
          newConversation
        };
      } else {
        messageApi.error('创建新会话失败，无法发送消息');
        return { success: false };
      }
    } catch (error) {
      console.error('创建新会话出错:', error);
      messageApi.error('创建新会话时发生错误，无法发送消息');
      return { success: false };
    }
  }, [dispatch, messageApi, todayStr]);

  return {
    chatIdRef,
    handleConversationClick,
    handleAddConversation,
    handleDeleteConversation,
    createConversationIfNeeded
  };
};

export default useConversationActions;
