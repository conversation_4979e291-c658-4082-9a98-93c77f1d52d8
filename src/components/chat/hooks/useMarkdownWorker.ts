import DOMPurify from 'dompurify';
import { useEffect, useRef, useState } from 'react';
import getMarkdownWorkerPool from '../utils/MarkdownWorkerPool';
import { isWorkerSupported } from '../utils/workerHelper';



// 取消错误的接口定义
interface CancellationError extends Error {
  isCancellation: boolean;
}

// Worker响应数据类型
interface WorkerResponseData {
  id: string;
  html?: string;
  error?: string;
  success: boolean;
}

export interface UseMarkdownWorkerOptions {
  content: string;
  onProcessed?: (content: string, success: boolean) => void;
}

export interface UseMarkdownWorkerResult {
  isLoading: boolean;
  processedHTML: string;
  error: string | null;
}

/**
 * Markdown Worker 处理 Hook
 * 
 * 用于封装 Markdown 渲染的 Worker 处理逻辑，包括：
 * - Worker 池管理
 * - 加载状态控制
 * - 错误处理
 * - 超时保护
 * - 备选同步渲染机制
 */
export function useMarkdownWorker({
  content,
  onProcessed
}: UseMarkdownWorkerOptions): UseMarkdownWorkerResult {
  // 状态变量
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [processedHTML, setProcessedHTML] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // 记录markdown内容的引用，用于避免重复处理相同内容
  const contentRef = useRef<string>('');
  // 记录当前任务ID
  const taskIdRef = useRef<string>('');
  
  // 检查浏览器Worker支持情况
  useEffect(() => {
    if (!isWorkerSupported()) {
      setIsLoading(false);
      setError('浏览器不支持Web Workers，渲染可能会较慢');
    }
  }, []);

  // 当内容变化时处理 Markdown
  useEffect(() => {
    // 如果内容为空或未变化，无需处理
    if (!content || content === contentRef.current) {
      return;
    }


    // 更新内容引用
    contentRef.current = content;

    // 设置加载状态
    setIsLoading(true);
    setError(null);

    // 生成唯一的任务ID
    const taskId = `markdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    taskIdRef.current = taskId;

    // 设置超时保护
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
      setError('处理超时，请尝试简化内容');

      // 超时时取消任务
      getMarkdownWorkerPool().cancelTask(taskId);

      if (onProcessed) {
        onProcessed(content, false);
      }
    }, 10000); // 10秒超时

    // 使用Worker池处理Markdown
    getMarkdownWorkerPool().processMarkdown(content, taskId)
      .then((result: WorkerResponseData) => {
        // 清除超时器
        clearTimeout(timeoutId);
        // 处理结果
        if (result.success && result.html) {
          // 在主线程中使用DOMPurify进行安全处理
          const sanitizedHTML = DOMPurify.sanitize(result.html);
          setProcessedHTML(sanitizedHTML);
          setError(null);
          setIsLoading(false);

          // 处理成功时调用回调
          if (onProcessed) {
            onProcessed(content, true);
          }
        } else if (result.error) {
          setError(`Markdown处理错误: ${result.error}`);
          setIsLoading(false);

          // 处理失败时也调用回调
          if (onProcessed) {
            onProcessed(content, false);
          }
        }
      })
      .catch((err: Error | CancellationError) => {
        // 清除超时器
        clearTimeout(timeoutId);

        // 判断是否为任务取消错误
        const isCancellationError = (err as CancellationError).isCancellation === true;

        if (!isCancellationError) {
          // 非取消错误才记录和显示
          console.error('[Markdown] Worker池错误:', err);
          setError(`处理错误: ${err.message || '未知错误'}`);
        } else {
          console.log('[Markdown] 任务已取消');
        }

        setIsLoading(false);

        // Worker错误时调用回调（任务取消也调用但不显示错误)
        if (onProcessed) {
          onProcessed(content, isCancellationError);
        }
      });

    // 清理函数
    return () => {
      clearTimeout(timeoutId);

      // **重要修改** - 避免在组件卸载时取消任务，让Worker完成处理
      console.log(`[组件清理] 保留Markdown处理任务: ${taskId}`);
      
      // 如果确实需要完全取消任务，请使用下面代码
      // if (taskIdRef.current === taskId) {
      //   console.log(`[组件卸载] 取消Markdown任务: ${taskId}`);
      //   getMarkdownWorkerPool().cancelTask(taskId);
      // }
    };
  }, [content, onProcessed]);

  // 确保在HTML处理完成后重置加载状态
  useEffect(() => {
    if (processedHTML) {
      setIsLoading(false);
      // 调用处理完成回调
      if (onProcessed) {
        onProcessed(content, true);
      }
    }
  }, [processedHTML, content, onProcessed]);

  // 如果没有处理过的HTML，则返回空字符串
  const finalHTML = processedHTML || '';

  return {
    isLoading,
    processedHTML: finalHTML,
    error
  };
}

export default useMarkdownWorker;
