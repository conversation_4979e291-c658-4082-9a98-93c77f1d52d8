import { useState, useCallback } from 'react';
import { getConversations, convertApiConversationToUiItem } from '../services/api';

/**
 * 自定义Hook: 管理会话列表
 * 
 * @param dispatch 状态分发函数
 * @param todayStr 今天的日期字符串
 * @param yesterdayStr 昨天的日期字符串
 * @returns { loading, error, fetchConversations, resetActiveChat }
 */
export const useConversations = (
  dispatch: React.Dispatch<any>,
  todayStr: string,
  yesterdayStr: string
) => {
  // 内部状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  /**
   * 重置活动会话
   */
  const resetActiveChat = useCallback((setMessages: (messages: any[]) => void) => {
    dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: '' });
    setMessages([]);
  }, [dispatch]);
  
  /**
   * 获取会话列表
   */
  const fetchConversations = useCallback(async (setMessages?: (messages: any[]) => void) => {
    // 标记组件是否已挂载，防止内存泄漏
    let isMounted = true;
    
    setLoading(true);
    setError(null);
    
    try {
      // 获取会话数据
      const response = await getConversations();
      if (!isMounted) return;
      
      // 处理成功响应
      if (response.code === 1000 && response.data.list) {
        // 将 API 响应转换为 UI 需要的格式
        const uiConversations = response.data.list.map(item =>
          convertApiConversationToUiItem(item, todayStr, yesterdayStr)
        );
        
        // 更新会话列表
        dispatch({ type: 'SET_CONVERSATIONS', payload: uiConversations });
        
        // 初始不选中任何会话，保持消息页面为空白状态
        if (setMessages) {
          dispatch({ type: 'SET_ACTIVE_CHAT_ID', payload: '' });
          setMessages([]);
        }
      } else {
        // 处理错误响应
        setError('获取会话列表失败');
      }
    } catch (error) {
      // 处理异常
      if (!isMounted) return;
      console.error('获取会话列表出错:', error);
      setError('获取会话列表出错');
    } finally {
      // 无论成功失败，都关闭加载状态
      if (isMounted) {
        setLoading(false);
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
    
    // 返回清理函数
    return () => {
      isMounted = false;
    };
  }, [dispatch, todayStr, yesterdayStr]);
  
  return {
    loading,
    error,
    fetchConversations,
    resetActiveChat
  };
};

export default useConversations;
