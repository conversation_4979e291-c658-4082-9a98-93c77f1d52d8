import { Message as BaseMessage } from '../types';

/**
 * 聚合消息类型定义
 * 兼容现有Message类型，同时扩展虚拟滚动所需的字段
 */
export interface VirtualMessage extends BaseMessage {
  /** 发送者姓名 */
  sender?: string;
  /** 发送时间 */
  time?: string;
  /** 快速访问消息内容的属性（兼容现有API） */
  content?: string;
  /** 附加信息 */
  metadata?: Record<string, unknown>;
}

/**
 * 类型转换工具函数
 * 将现有Message转换为VirtualMessage
 */
export const convertToVirtualMessage = (msg: BaseMessage): VirtualMessage => {
  return {
    ...msg,
    content: msg.message, // 将message字段映射到content
    sender: msg.type === 'user' ? '用户' : 'AI助手',
    time: new Date().toLocaleTimeString()
  };
};

/**
 * 批量转换消息数组
 */
export const convertMessagesToVirtual = (messages: BaseMessage[]): VirtualMessage[] => {
  return messages.map(convertToVirtualMessage);
};
