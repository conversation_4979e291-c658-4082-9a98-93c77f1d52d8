import { ConversationItem, Message } from '../types';

/**
 * 聊天状态类型定义
 */
export type ChatState = {
  messages: Message[];
  inputValue: string;
  activeChatId: string;
  loading: boolean;
  conversationsItems: ConversationItem[];
  sendingMessage: boolean; // 标记是否正在发送消息
};

/**
 * 聊天操作类型定义
 */
export type ChatAction =
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_INPUT'; payload: string }
  | { type: 'SET_ACTIVE_CHAT_ID'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CONVERSATIONS'; payload: ConversationItem[] }
  | { type: 'ADD_CONVERSATION'; payload: ConversationItem }
  | { type: 'UPDATE_CONVERSATION'; payload: ConversationItem } // 更新会话信息（标题等）
  | { type: 'DELETE_CONVERSATION'; payload: string }
  | { type: 'SET_SENDING_MESSAGE'; payload: boolean } // 设置是否正在发送消息
  | { type: 'CLEAR_MESSAGES' };

/**
 * 初始状态
 */
export const initialChatState: ChatState = {
  messages: [],
  inputValue: '',
  activeChatId: '', // 默认选中第一个会话
  loading: false,
  conversationsItems: [],
  sendingMessage: false, // 初始状态不在发送消息
};

/**
 * 聊天状态 reducer 函数
 */
export const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    case 'SET_INPUT':
      return { ...state, inputValue: action.payload };
    case 'SET_ACTIVE_CHAT_ID':
      return { ...state, activeChatId: action.payload };
    case 'SET_SENDING_MESSAGE':
      return { ...state, sendingMessage: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_CONVERSATIONS':
      return { ...state, conversationsItems: action.payload };
    case 'ADD_CONVERSATION':
      return { ...state, conversationsItems: [action.payload, ...state.conversationsItems] };
    case 'UPDATE_CONVERSATION':
      // 更新会话信息，主要用于更新会话标题
      return {
        ...state,
        conversationsItems: state.conversationsItems.map(item =>
          item.key === action.payload.key ? { ...item, ...action.payload } : item
        )
      };
    case 'DELETE_CONVERSATION':
      return {
        ...state,
        conversationsItems: state.conversationsItems.filter(item => item.key !== action.payload)
      };
    case 'CLEAR_MESSAGES':
      return { ...state, messages: [] };
    default:
      return state;
  }
};
