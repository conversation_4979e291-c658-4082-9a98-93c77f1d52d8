import {
  ApiResponse,
  ApiConversationListResponse,
  ApiConversationItem,
  ConversationItem,
  Message
} from '../types';
import {
  getConversations as fetchConversations,
  createConversation as apiCreateConversation,
  deleteConversation as apiDeleteConversation,
  getConversationById as apiGetConversationById,
  getChatMessages as fetchChatMessages,
  MessageType
} from '../../../services/conversationService';
import { isApiSuccess } from '../../../utils/apiUtils';

/**
 * 获取对话列表
 * @param page 页码
 * @param pageSize 每页大小
 * @returns Promise 包含对话列表
 */
export async function getConversations(page = 1, pageSize = 10): Promise<ApiResponse<ApiConversationListResponse>> {
  try {
    // 调用真实API
    const response = await fetchConversations(page, pageSize);

    // 将API响应转换为应用需要的格式
    const result: ApiResponse<ApiConversationListResponse> = {
      code: response.code,
      message: response.message,
      data: {
        list: response.data.items.map(item => ({
          id: item.id,
          uid: item.username,
          title: item.title,
          chatId: item.chatId,
          createTime: item.createTime
        }))
      }
    };

    return result;
  } catch (error) {
    console.error('获取对话列表失败:', error);
    throw error;
  }
}

/**
 * 将 API 会话项转换为 UI 会话项
 * @param apiItem API 会话项
 * @param todayStr 今天的日期字符串 (YYYY-MM-DD)
 * @param yesterdayStr 昨天的日期字符串 (YYYY-MM-DD)
 * @returns UI 会话项
 */
export function convertApiConversationToUiItem(
  apiItem: ApiConversationItem,
  todayStr: string,
  yesterdayStr: string
): ConversationItem {
  // 提取创建时间的日期部分 (YYYY-MM-DD)
  const dateStr = apiItem.createTime.split(' ')[0];

  // 确定分组 (今天/昨天/更早)
  let group = 'earlier';
  if (dateStr === todayStr) {
    group = 'today';
  } else if (dateStr === yesterdayStr) {
    group = 'yesterday';
  }

  // 获取时间戳用于排序
  const timestamp = new Date(apiItem.createTime).getTime();

  // 返回转换后的会话项
  return {
    key: apiItem.chatId, // 使用 chatId 作为唯一标识
    label: apiItem.title,
    date: dateStr,
    group,
    timestamp,
  };
}

/**
 * 创建新对话
 * @param message 可选的用户提问消息，用于生成更有意义的会话标题
 * @returns Promise 包含创建结果和对话信息
 */
export async function createNewConversation(message?: string): Promise<{success: boolean; conversation?: ApiConversationItem}> {
  try {
    // 传递用户消息给创建会话的 API
    const response = await apiCreateConversation(message);
    // 使用错误码检查工具验证响应状态
    const success = isApiSuccess(response.code);

    if (success && response.data) {
      // 将服务层的ConversationDTO转换为ApiConversationItem
      const conversation: ApiConversationItem = {
        id: response.data.id,
        uid: response.data.username,
        title: response.data.title,
        chatId: response.data.chatId,
        createTime: response.data.createTime
      };
      console.log('创建对话成功:', conversation.chatId);
      return { success, conversation };
    }

    return { success };
  } catch (error) {
    console.error('创建对话失败:', error);
    return { success: false };
  }
}

/**
 * 删除对话
 * @param id 对话ID
 * @returns Promise 包含删除结果
 */
export async function deleteConversation(id: string): Promise<boolean> {
  try {
    const response = await apiDeleteConversation(id);
    // 使用错误码检查工具验证响应状态
    return isApiSuccess(response.code) && response.data === true;
  } catch (error) {
    console.error('删除对话失败:', error);
    return false;
  }
}

/**
 * 将API消息类型转换为UI消息类型
 * @param messageType API消息类型
 * @returns UI消息类型 ('user' | 'assistant')
 */
function convertMessageType(messageType: MessageType): 'user' | 'assistant' {
  switch (messageType) {
    case MessageType.USER:
      return 'user';
    case MessageType.AI:
    case MessageType.SYSTEM:
    case MessageType.TOOL_EXECUTION_RESULT:
    case MessageType.CUSTOM:
    default:
      return 'assistant';
  }
}

/**
 * 解析消息内容，根据不同的消息格式提取文本
 * @param content 消息内容字符串，可能是JSON格式
 * @param messageType 消息类型
 * @returns 解析后的消息文本
 */
function parseMessageContent(content: string, messageType?: MessageType): string {
  try {
    // 尝试解析JSON
    const parsed = JSON.parse(content);

    // 根据消息类型和内容结构进行不同的处理
    if (messageType === MessageType.USER && parsed.contents && Array.isArray(parsed.contents) && parsed.contents.length > 0) {
      // 用户消息可能包含contents数组，取第一个元素的text字段
      const firstContent = parsed.contents[0];
      if (firstContent && firstContent.text) {
        return firstContent.text;
      }
    } else if (parsed.text) {
      // 其他类型消息直接取text字段
      return parsed.text;
    }

    // 如果没有找到合适的字段，返回原始内容
    return content;
  } catch (e) {
    // 如果解析失败，直接返回原始内容
    return content;
  }
}

/**
 * 获取单个会话详情
 * @param chatId 会话 ID
 * @returns Promise 包含会话详情
 */
export async function getConversationDetail(chatId: string): Promise<{success: boolean; conversation?: ApiConversationItem}> {
  try {
    const response = await apiGetConversationById(chatId);
    // 使用错误码检查工具验证响应状态
    const success = isApiSuccess(response.code);

    if (success && response.data) {
      // 将服务层的 ConversationDTO 转换为 ApiConversationItem
      const conversation: ApiConversationItem = {
        id: response.data.id,
        uid: response.data.username,
        title: response.data.title,
        chatId: response.data.chatId,
        createTime: response.data.createTime
      };
      return { success, conversation };
    }

    return { success };
  } catch (error) {
    console.error('获取会话详情失败:', error);
    return { success: false };
  }
}

/**
 * 获取指定会话的消息历史
 * @param chatId 会话ID
 * @returns Promise 包含消息列表
 */
export async function getChatMessages(chatId: string): Promise<Message[]> {
  try {
    const response = await fetchChatMessages(chatId);

    // 检查响应状态
    if (!isApiSuccess(response.code) || !response.data) {
      console.error('获取消息历史失败:', response.message);
      return [];
    }

    // 过滤掉SYSTEM类型的消息，并将API响应转换为应用需要的格式
    return response.data
      .filter(item => item.messageType !== MessageType.SYSTEM) // 过滤掉SYSTEM类型的消息
      .map(item => ({
        id: item.id,
        message: parseMessageContent(item.content, item.messageType as MessageType),
        type: convertMessageType(item.messageType as MessageType)
      }));
  } catch (error) {
    console.error(`获取会话(${chatId})的消息历史失败:`, error);
    return [];
  }
}
