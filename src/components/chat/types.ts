// API 会话项类型
export interface ApiConversationItem {
  id: number;
  uid: string;
  title: string;
  chatId: string;
  createTime: string;
}

// API 响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  total?: number;
  page?: number;
  pageSize?: number;
}

// API 会话列表响应
export interface ApiConversationListResponse {
  list: ApiConversationItem[];
}

// API 消息类型枚举
export enum ApiMessageType {
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  AI = 'AI',
  TOOL_EXECUTION_RESULT = 'TOOL_EXECUTION_RESULT',
  CUSTOM = 'CUSTOM'
}

// API 消息项类型
export interface ApiMessageItem {
  id: string;
  username: string;
  chatId: string;
  messageType: ApiMessageType;
  content: string;
  createTime: string;
}

// API 消息列表响应
export interface ApiMessageListResponse {
  list: ApiMessageItem[];
}

// 会话项类型 (用于UI展示)
export interface ConversationItem {
  key: string;
  label: string;
  date: string;
  group: string; // 分组标识：'today', 'yesterday', 'earlier'
  timestamp?: number; // 可选的时间戳，用于排序
}

// 消息类型
export interface Message {
  id: string;
  message: string;
  type: 'user' | 'assistant';
}

// 提示词项类型
export interface PromptItem {
  key: string;
  label?: React.ReactNode;
  description: string;
  icon?: React.ReactNode;
  children?: PromptItem[];
}
