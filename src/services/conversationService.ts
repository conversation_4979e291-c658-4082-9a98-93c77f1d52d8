/**
 * 对话会话服务
 * 提供与后端API交互的方法，包括创建会话、获取会话列表和删除会话
 */
import axios, { AxiosInstance } from 'axios';
import { getAuthTicket, handleUnauthorized } from './authService';
import { API_PATHS } from '../config/api.config';

// API响应的通用接口
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
  traceId: string;
}

// 会话数据接口
export interface ConversationDTO {
  id: number;
  chatId: string;
  username: string;
  title: string;
  createTime: string;
  updateTime: string;
}

// 消息类型枚举
export enum MessageType {
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  AI = 'AI',
  TOOL_EXECUTION_RESULT = 'TOOL_EXECUTION_RESULT',
  CUSTOM = 'CUSTOM'
}

// 消息数据接口
export interface MessageDTO {
  id: string;
  username: string;
  chatId: string;
  messageType: MessageType;
  content: string;
  createTime: string;
}

// 分页结果接口
export interface PagedResult<T> {
  items: T[];
  total: number;
  pageSize: number;
  page: number;
  totalPage: number;
}

// 使用统一配置的API路径
const API_BASE_URL = API_PATHS.API;

// 创建axios实例
const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
});

// 请求拦截器：自动带上ticket
axiosInstance.interceptors.request.use(config => {
  const ticket = getAuthTicket();
  if (ticket) {
    // 将ticket设置在authorization请求头中
    config.headers = config.headers || {};
    config.headers['Authorization'] = ticket;
  }
  return config;
});

// 响应拦截器：处理未授权情况
axiosInstance.interceptors.response.use(
  response => {
    // 检查接口返回的code
    if (response.data && response.data.code !== undefined) {
      handleUnauthorized(response.data.code);
    }
    return response;
  },
  error => {
    // 处理请求错误
    console.error('API请求错误:', error);
    if (error.response && error.response.data) {
      handleUnauthorized(error.response.data.code || 0);
    }
    return Promise.reject(error);
  }
);

/**
 * 创建新对话
 * @param message 用户提问的消息内容，用于生成会话标题
 * @returns Promise<ApiResponse<ConversationDTO>>
 */
export const createConversation = async (message?: string): Promise<ApiResponse<ConversationDTO>> => {
  try {
    // 传递 message 参数，后端可以根据内容生成更有意义的标题
    const response = await axiosInstance.post<ApiResponse<ConversationDTO>>('/chats', { 
      message: message || '' // 如果没有提供消息，则传空字符串
    });
    return response.data;
  } catch (error) {
    console.error('创建对话失败:', error);
    throw error;
  }
};

/**
 * 获取对话列表（分页）
 * @param page 页码，默认为1
 * @param pageSize 每页数量，默认为10
 * @returns Promise<ApiResponse<PagedResult<ConversationDTO>>>
 */
/**
 * 获取单个会话详情
 * @param chatId 会话 ID
 * @returns Promise<ApiResponse<ConversationDTO>>
 */
export const getConversationById = async (chatId: string): Promise<ApiResponse<ConversationDTO>> => {
  try {
    const response = await axiosInstance.get<ApiResponse<ConversationDTO>>(`/chats/${chatId}`);
    return response.data;
  } catch (error) {
    console.error('获取会话详情失败:', error);
    throw error;
  }
};

/**
 * 获取会话列表
 * @returns Promise<ApiResponse<PagedResult<ConversationDTO>>>
 */
export const getConversations = async (
  page: number = 1,
  pageSize: number = 10
): Promise<ApiResponse<PagedResult<ConversationDTO>>> => {
  try {
    const response = await axiosInstance.get<ApiResponse<PagedResult<ConversationDTO>>>('/chats', {
      params: { page, pageSize }
    });
    return response.data;
  } catch (error) {
    console.error('获取对话列表失败:', error);
    throw error;
  }
};

/**
 * 获取指定会话的消息历史
 * @param chatId 会话ID
 * @returns Promise<ApiResponse<MessageDTO[]>>
 */
export const getChatMessages = async (chatId: string): Promise<ApiResponse<MessageDTO[]>> => {
  try {
    const response = await axiosInstance.get<ApiResponse<MessageDTO[]>>(`/chats/${chatId}/messages`);
    return response.data;
  } catch (error) {
    console.error(`获取会话(${chatId})的消息历史失败:`, error);
    throw error;
  }
};

/**
 * 删除对话
 * @param id 对话ID
 * @returns Promise<ApiResponse<boolean>>
 */
export const deleteConversation = async (id: string): Promise<ApiResponse<boolean>> => {
  try {
    const response = await axiosInstance.delete<ApiResponse<boolean>>(`/chats/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除对话失败:', error);
    throw error;
  }
};
