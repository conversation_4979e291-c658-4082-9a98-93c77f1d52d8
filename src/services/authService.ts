/**
 * 用户鉴权与CAS登录相关服务
 * 负责ticket检测、CAS跳转、token拉取、localStorage管理
 */

import { API_ERROR_CODES, isApiSuccess } from '../utils/apiUtils';
import { API_PATHS } from '../config/api.config';

// 使用统一配置的API路径
const CAS_LOGIN_URL = API_PATHS.CAS_LOGIN;
const TOKEN_API_URL = API_PATHS.TOKEN;

const LOCAL_KEY = 'fans_omnioview_user';

export interface AuthUser {
  name: string;
  ticket: string;
}

/**
 * 从localStorage获取用户信息
 */
function getLocalUser(): AuthUser | null {
  const str = localStorage.getItem(LOCAL_KEY);
  if (!str) return null;
  try {
    return JSON.parse(str);
  } catch {
    return null;
  }
}

/**
 * 保存用户信息到localStorage
 */
function setLocalUser(user: AuthUser) {
  localStorage.setItem(LOCAL_KEY, JSON.stringify(user));
}

/**
 * 清除本地用户信息
 */
function clearLocalUser() {
  localStorage.removeItem(LOCAL_KEY);
}

/**
 * 从URL中获取ticket参数
 */
function getTicketFromUrl(): string | null {
  const params = new URLSearchParams(window.location.search);
  return params.get('ticket');
}

/**
 * 根据ticket获取用户token信息
 */
async function fetchToken(ticket: string): Promise<AuthUser> {
  const res = await fetch(`${TOKEN_API_URL}?ticket=${encodeURIComponent(ticket)}`);
  const data = await res.json();
  if (data.code === 1000 && data.data && data.data.name) {
    return { name: data.data.name, ticket };
  }
  throw new Error('授权失败');
}

/**
 * 检查本地鉴权状态，无则自动跳转CAS登录
 * 授权回调后自动拉取token并保存
 */
export async function ensureAuth() {
  let user = getLocalUser();
  if (user && user.ticket) return user;

  const ticket = getTicketFromUrl();
  if (!ticket) {
    // 跳转CAS登录
    window.location.href = CAS_LOGIN_URL;
    return;
  }

  // 用ticket换token
  try {
    user = await fetchToken(ticket);
    setLocalUser(user);
    // 清理URL中的ticket参数
    const url = new URL(window.location.href);
    url.searchParams.delete('ticket');
    url.searchParams.delete('ext');
    window.history.replaceState({}, '', url.toString());
    return user;
  } catch {
    clearLocalUser();
    window.location.href = CAS_LOGIN_URL;
  }
}

/**
 * 获取当前ticket
 */
export function getAuthTicket(): string | null {
  const user = getLocalUser();
  return user?.ticket || null;
}

/**
 * 获取当前用户名
 */
export function getUserName(): string | null {
  const user = getLocalUser();
  return user?.name || null;
}

/**
 * 判断是否已登录
 */
export function isAuthenticated(): boolean {
  return getLocalUser() !== null;
}

/**
 * 退出登录
 */
export function logout() {
  clearLocalUser();
  window.location.href = CAS_LOGIN_URL;
}

/**
 * 处理未授权响应
 * @param code 接口返回的状态码
 */
export function handleUnauthorized(code: number) {
  // 判断是否未授权状态码
  if (!isApiSuccess(code) && code === API_ERROR_CODES.UNAUTHORIZED) {
    console.log('检测到未授权状态，清除登录信息并重新登录');
    logout();
  }
}
