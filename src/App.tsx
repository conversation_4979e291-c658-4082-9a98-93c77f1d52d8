import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { useEffect, useState } from 'react'
import OpenAIChatPage from './components/chat/OpenAIChatPage'
import { ensureAuth } from './services/authService'
import './App.css'

function App() {
  // 用于控制加载状态
  const [loading, setLoading] = useState<boolean>(true);
  
  // 在组件加载时检查验证状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查是否登录，未登录会自动重定向到CAS登录页
        await ensureAuth();
        setLoading(false);
      } catch (error) {
        console.error('验证失败:', error);
      }
    };
    
    checkAuth();
  }, []);
  
  // 如果正在验证，显示加载中
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <p>正在验证身份...</p>
      </div>
    );
  }
  
  return (
    <ConfigProvider locale={zhCN}>
      <div className="app-container" style={{ position: 'relative', width: '100%', height: '100vh' }}>
        <OpenAIChatPage />
      </div>
    </ConfigProvider>
  )
}

export default App
