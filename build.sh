#!/bin/bash

# Build script for Fans Omniview frontend
# Creates a deployment package with necessary files

# Get environment parameter
ENV=${1:-prod}  # Default to production environment
echo "Building deployment package for ${ENV} environment"

# Use fixed package name
PACKAGE_NAME="fans-omniview-frontend.tgz"

# Create temporary directory
TEMP_DIR="./temp_build"
rm -rf $TEMP_DIR
mkdir -p $TEMP_DIR

# Install dependencies
echo "Installing dependencies..."
npm install

# Build project based on environment
echo "Building project..."
if [ "$ENV" = "dev" ]; then
    npm run build:dev
else
    npm run build
fi

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "Error: Build failed, dist directory does not exist"
    exit 1
fi

# Copy necessary files to temp directory
echo "Copying files to temp directory..."
cp -r dist $TEMP_DIR/
cp -r conf $TEMP_DIR/
cp docker-compose.yml $TEMP_DIR/
cp deploy.sh $TEMP_DIR/

# Environment variables are already injected in the built frontend files
# No need to copy environment files

# Create README file
cat > $TEMP_DIR/README.txt << EOF
Fans Omniview Frontend Deployment Package
Environment: ${ENV}
Build Time: $(date "+%Y-%m-%d %H:%M:%S")

Deployment Instructions:
1. Extract this archive
2. Navigate to the extracted directory
3. Deploy using the included deploy.sh script

Contact the development team if you have any issues.
EOF

# Create deployment package
echo "Creating deployment package..."
tar -czf $PACKAGE_NAME -C $TEMP_DIR .

# Clean up temporary directory
rm -rf $TEMP_DIR

echo "Deployment package created successfully: ${PACKAGE_NAME}"
echo "=========================================="
echo "Package contents:"
echo "- dist/ (Built frontend assets)"
echo "- conf/ (Nginx configuration)"
echo "- docker-compose.yml (Docker compose configuration)"
echo "- deploy.sh (Deployment script)"
echo "=========================================="
