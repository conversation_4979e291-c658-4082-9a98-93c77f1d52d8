# Fans Omniview 前端项目

本项目是基于 React + TypeScript + Vite 构建的 Fans Omniview 前端应用。

## 技术栈

- **前端框架**: React 19
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design
- **部署工具**: Docker + Nginx

## 目录结构

```text
.
├── .env               # 基础环境变量（所有环境共用）
├── .env.local         # 本地开发环境变量（可能在.gitignore中）
├── .env.dev          # 开发环境变量
├── .env.prod         # 生产环境变量
├── src/              # 源代码目录
├── public/           # 静态资源目录
├── conf/             # Nginx配置目录
├── deploy.sh          # 部署脚本
├── docker-compose.yml # Docker编排配置
├── package.json      # 项目依赖配置
└── vite.config.ts     # Vite配置文件
```

## 开发指南

### 开发环境要求

- Node.js 18+
- npm 8+
- Docker（用于容器部署）

### 本地开发

```bash
# 安装依赖
npm install

# 本地开发环境启动（使用 .env.local 配置）
npm run local

# 开发环境启动（使用 .env.dev 配置）
npm run dev
```

### 构建项目

```bash
# 开发环境构建（使用 .env.dev 配置）
npm run build:dev

# 生产环境构建（使用 .env.prod 配置）
npm run build
```

## 部署指南

### 使用部署脚本

1. 给脚本添加执行权限：

    ```bash
    chmod +x deploy.sh
    ```

2. 运行部署脚本：

    ```bash
    # 部署到生产环境（使用 .env.prod 配置）
    ./deploy.sh prod

    # 或部署到开发环境（使用 .env.dev 配置）
    ./deploy.sh dev
    ```

### 手动部署

1. 构建前端项目：

    ```bash
    npm install
    npm run build     # 生产环境构建（使用 .env.prod）
    # 或
    npm run build:dev # 开发环境构建（使用 .env.dev）
    ```

2. 启动 Docker 容器：

    ```bash
    # 使用生产环境配置
    docker-compose --env-file .env.prod up -d

    # 或使用开发环境配置
    docker-compose --env-file .env.dev up -d
    ```

## 环境变量配置

项目使用了 Vite 的标准环境变量加载机制，环境变量配置使用如下文件：

- `.env`：基础变量，所有环境共用，始终会被加载
- `.env.local`：本地开发环境变量，只在本地生效，且被添加到 `.gitignore` 中
- `.env.dev`：开发环境变量，使用 `--mode dev` 加载
- `.env.prod`：生产环境变量，使用 `--mode prod` 加载

### 环境变量列表

所有要暴露给前端应用的变量必须以 `VITE_` 开头：

- `VITE_APP_ENV`：前端环境标识（development/production）
- `VITE_APP_BASE_URL`：API 基础路径
- `VITE_APP_API_PATH`：API 路径
- `VITE_APP_TOKEN_PATH`：Token 路径
- `VITE_APP_CAS_BASE_URL`：CAS 登录地址
- `VITE_APP_CALLBACK_URL`：回调地址
- `VITE_APP_OPENAI_BASE_URL`：OpenAI API 基础路径
- `VITE_APP_OPENAI_API_KEY`：OpenAI API 密钥

### Vite 对环境变量的处理

1. 加载顺序：
   - `.env`
   - `.env.local`
   - `.env.[mode]`
   - `.env.[mode].local`

2. 注意事项：
   - 不支持在 `.env` 文件中设置 `NODE_ENV=production`
   - 仅 `VITE_` 开头的变量会被包含在客户端代码中

### 访问部署的应用

部署成功后，可以通过以下方式访问应用：

- 开发环境：访问 `http://localhost:8000`
- 生产环境：访问 `http://localhost:80`

## Nginx 配置

`conf/nginx.conf` 文件包含 Web 服务器配置，主要功能：

- 处理 SPA 路由重定向
- 配置静态资源缓存
- 配置 API 请求代理
- 开启 Gzip 压缩
